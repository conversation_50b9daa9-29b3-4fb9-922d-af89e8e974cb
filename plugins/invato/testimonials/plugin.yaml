plugin:
    name: 'invato.testimonials::lang.plugin.name'
    description: 'invato.testimonials::lang.plugin.description'
    author: Invato
    icon: oc-icon-quote-left
    homepage: ''
permissions:
    'invato.testimonials.manage_plugin':
        tab: 'invato.testimonials::lang.plugin.name'
        label: 'invato.testimonials::lang.permissions.manage_plugin'
    'invato.testimonials.import_reviews':
        tab: 'invato.testimonials::lang.plugin.name'
        label: 'invato.testimonials::lang.permissions.import_reviews'
    'invato.testimonials.export_reviews':
        tab: 'invato.testimonials::lang.plugin.name'
        label: 'invato.testimonials::lang.permissions.export_reviews'
    'invato.testimonials.manage_reviews':
        tab: 'invato.testimonials::lang.plugin.name'
        label: 'invato.testimonials::lang.permissions.manage_reviews'
    'invato.testimonials.approve_reviews':
        tab: 'invato.testimonials::lang.plugin.name'
        label: 'invato.testimonials::lang.permissions.approve_reviews'
navigation:
    testimonials:
        label: 'invato.testimonials::lang.plugin.name'
        url: /
        icon: icon-quote-left
        iconSvg: plugins/invato/testimonials/assets/images/invato-testimonials.svg
        permissions:
            - 'invato.testimonials.manage_plugin'
        sideMenu:
            content-section:
                label: 'Content'
                itemType: section

            reviews:
                label: 'invato.testimonials::lang.menu.reviews'
                url: invato/testimonials/reviews
                icon: icon-quote-left
                permissions:
                    - 'invato.testimonials.manage_plugin'

            documentation-section:
                label: 'Documentation'
                itemType: section

            readme:
                label: 'Readme'
                url: invato/testimonials/readme
                icon: icon-book
                permissions:
                    - 'superusers.view_readme'
            manual:
                label: 'Manual'
                url: invato/testimonials/manual
                icon: icon-book
                permissions:
                    - 'invato.testimonials.manage_plugin'
