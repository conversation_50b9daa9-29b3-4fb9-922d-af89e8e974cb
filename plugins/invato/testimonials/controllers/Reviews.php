<?php

namespace Invato\Testimonials\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Invato\Testimonials\Models\Review;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;

class Reviews extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static string $modelClass = Review::class;

    public string $formConfig = 'config_form.yaml';

    public string $listConfig = 'config_list.yaml';

    public string $importExportConfig = 'config_import_export.yaml';

    public string $importPermission = 'invato.testimonials.import_reviews';

    public string $exportPermission = 'invato.testimonials.export_reviews';

    public $requiredPermissions = [
        'invato.testimonials.manage_plugin',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Testimonials', 'testimonials', 'reviews');
    }

    // START Testimonials Specific function.

    // END Testimonials Specific function.
}
