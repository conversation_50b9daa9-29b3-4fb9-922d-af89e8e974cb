list: $/invato/testimonials/models/review/columns.yaml
modelClass: Invato\Testimonials\Models\Review
title: Reviews
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/testimonials/reviews/update/:id'
structure:
    showTree: false
    showReorder: true
    showSorting: true
# BEGIN Skeleton Soft Deletes
filter: $/invato/testimonials/models/review/scopes.yaml
# END Skeleton Soft Deletes
