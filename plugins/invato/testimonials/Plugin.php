<?php

namespace Invato\Testimonials;

use Invato\Testimonials\Components\ReviewDetail;
use Invato\Testimonials\Components\ReviewList;
use Invato\Testimonials\Models\Review;
use Invato\Testimonials\Models\TestimonialsSettings;
use Invato\Traits\RegistersPageFinderTrait;
use Invato\Traits\SettingsMenuContextTrait;
use System\Classes\PluginBase;

class Plugin extends PluginBase
{
    use RegistersPageFinderTrait;
    use SettingsMenuContextTrait;

    public function boot(): void
    {
        $this->setupSettingsMenuContext('settings');
        $this->registerPageFinder();
    }

    public function register(): void {}

    public function registerComponents(): array
    {
        return [
            ReviewDetail::class => 'ReviewDetail',
            ReviewList::class => 'ReviewList',
        ];
    }

    public function registerSettings(): array
    {
        return [
            'settings' => [
                'label' => trans('invato.testimonials::lang.settings.label'),
                'description' => trans('invato.testimonials::lang.settings.description'),
                'category' => 'Plugins',
                'icon' => 'icon-quote-left',
                'class' => TestimonialsSettings::class,
            ],
        ];
    }

    /**
     * Get PageFinder configuration for this plugin
     */
    protected function getPageFinderConfig(): array
    {
        return [
            'model' => Review::class,
            'menu_types' => [
                'testimonials-review' => 'invato.testimonials::lang.menu_types.review',
                'all-testimonials-reviews' => 'invato.testimonials::lang.menu_types.all_reviews',
            ]
        ];
    }
}
