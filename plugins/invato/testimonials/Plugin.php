<?php

namespace Invato\Testimonials;

use Invato\Testimonials\Components\ReviewDetail;
use Invato\Testimonials\Components\ReviewList;
use Invato\Testimonials\Models\Review;
use Invato\Traits\RegistersPageFinderTrait;
use System\Classes\PluginBase;

class Plugin extends PluginBase
{
    use RegistersPageFinderTrait;

    public function boot(): void
    {
        $this->registerPageFinder();
    }

    public function register(): void {}

    public function registerComponents(): array
    {
        return [
            ReviewDetail::class => 'ReviewDetail',
            ReviewList::class => 'ReviewList',
        ];
    }

    /**
     * Get PageFinder configuration for this plugin
     */
    protected function getPageFinderConfig(): array
    {
        return [
            'model' => Review::class,
            'menu_types' => [
                'testimonials-review' => 'invato.testimonials::lang.menu_types.review',
                'all-testimonials-reviews' => 'invato.testimonials::lang.menu_types.all_reviews',
            ]
        ];
    }
}
