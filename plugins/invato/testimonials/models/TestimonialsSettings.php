<?php

namespace Invato\Testimonials\Models;

use Cms\Classes\Page;
use System\Behaviors\SettingsModel;

class TestimonialsSettings extends SettingsModel
{
    public string $settingsCode = 'invato_testimonials_settings';

    public string $settingsFields = 'fields.yaml';

    public function initSettingsData(): void
    {
        $this->testimonialsPage = 'testimonials/testimonials';
        $this->reviewPage = 'testimonials/review';
    }

    public function getTestimonialsPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }

    public function getReviewPageOptions()
    {
        return Page::sortBy('baseFileName')->lists('baseFileName', 'baseFileName');
    }
}
