<?php

namespace Invato\Testimonials\Models;

use Backend\Models\ImportModel;
use Exception;

class ReviewImportModel extends ImportModel
{
    public $rules = [
        'name' => 'required|string|max:255',
    ];

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if review with this name/slug already exists
                $slug = $data['slug'] ?? str_slug($data['name']);
                $review = Review::withTrashed()->where('slug', $slug)->first();

                if (! $review) {
                    $review = new Review;
                }

                $review->fill([
                    'name' => $data['name'] ?? null,
                    'slug' => $slug ?? null,
                    'date' => $data['date'] ?? null,
                    'score' => $data['score'] ?? 0,
                    'review' => $data['review'] ?? null,
                    'is_approved' => $data['is_approved'] ?? false,
                    'sort_order' => $data['sort_order'] ?? 0,
                ]);

                $review->save();

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
