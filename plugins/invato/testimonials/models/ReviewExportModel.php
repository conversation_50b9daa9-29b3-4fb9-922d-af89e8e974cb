<?php

namespace Invato\Testimonials\Models;

use Backend\Models\ExportModel;
use JsonException;

class ReviewExportModel extends ExportModel
{
    /**
     * @throws JsonException
     */
    public function exportData($columns, $sessionKey = null): array
    {
        $reviews = Review::all();
        $exportData = [];

        foreach ($reviews as $review) {
            $record = [];

            foreach ($columns as $column) {
                // Handle special cases
                match ($column) {
                    default => $record[$column] = $review->{$column},
                };
            }

            $exportData[] = $record;
        }

        return $exportData;
    }
}
