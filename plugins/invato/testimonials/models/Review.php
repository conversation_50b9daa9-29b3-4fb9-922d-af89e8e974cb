<?php

namespace Invato\Testimonials\Models;

use Invato\Testimonials\Components\ReviewDetail;
use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;

class Review extends Model
{
    // Begin Skeleton model
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function ($review) {
            static::createRedirect(
                plugin: 'testimonials',
                modelRecord: $review,
                detailPageController: ReviewDetail::class,
                status: 301
            );
        });

        static::restored(static function ($review) {
            static::deleteRedirect($review);
        });
    }

    public $table = 'invato_testimonials_reviews';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'name' => 'string',
        'slug' => 'string',
        'date' => 'date',
        'score' => 'integer',
        'review' => 'string',
        'is_approved' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'name',
        'slug',
        'date',
        'score',
        'review',
        'is_approved',
        'sort_order',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'name' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_testimonials_reviews,slug,{{id}}'],
        'date' => ['nullable', 'date'],
        'score' => ['nullable', 'integer', 'min:1', 'max:5'],
        'review' => ['nullable', 'string'],
        'is_approved' => ['boolean'],
        'sort_order' => ['integer'],
    ];

    // translatable
    public $implement = [
        \RainLab\Translate\Behaviors\TranslatableModel::class
    ];
    public $translatable = [
        'name',
        'slug',
        'review',
    ];

    protected array $slugs = [
        'slug' => 'name',
    ];

    // These attributes should not be in $casts and $rules
    protected $jsonable = [];

    /**
     * Get PageFinder configuration for Review model
     */
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'testimonials-review',
            'all_type' => 'all-testimonials-reviews',
            'component' => 'ReviewDetail',
        ];
    }

    // END Skeleton model

    // BEGIN Model specific
    // END Model specific
}
