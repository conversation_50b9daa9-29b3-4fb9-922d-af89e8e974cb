# Invato Testimonials Plugin

## Overzicht

De Invato Testimonials plugin biedt een complete oplossing voor het beheren van klantrecensies en testimonials op uw OctoberCMS website. Deze plugin volgt de Invato standaarden en biedt uitgebreide functionaliteit voor het verzamelen, modereren en tonen van klantfeedback.

## Functionaliteiten

### Review Management
- **CRUD operaties**: Volledig beheer van reviews (Create, Read, Update, Delete)
- **Soft Delete**: Reviews kunnen worden verwijderd en hersteld
- **Goedkeuringsproces**: Reviews kunnen worden goedgekeurd voordat ze zichtbaar zijn
- **Scoring systeem**: 1-5 sterren beoordeling
- **Sorteerbare lijst**: Reviews kunnen worden gesorteerd op volgorde

### Import/Export
- **CSV Import**: Bulk import van reviews via CSV bestanden
- **CSV Export**: Export van alle reviews naar CSV formaat
- **Validatie**: Automatische validatie tijdens import proces
- **Error handling**: Duidelijke foutmeldingen bij import problemen

### Frontend Components
- **ReviewList**: Component voor het tonen van een lijst met reviews
- **ReviewDetail**: Component voor het tonen van individuele review details

### Permissions
- `invato.testimonials.manage_plugin`: Algemeen beheer van de plugin
- `invato.testimonials.manage_reviews`: Beheer van reviews
- `invato.testimonials.approve_reviews`: Goedkeuren van reviews
- `invato.testimonials.import_reviews`: Importeren van reviews
- `invato.testimonials.export_reviews`: Exporteren van reviews

## Database Schema

### Reviews Tabel (`invato_testimonials_reviews`)
- `id`: Primary key
- `name`: Naam van de reviewer (verplicht)
- `slug`: URL-vriendelijke slug (automatisch gegenereerd)
- `date`: Datum van de review
- `score`: Score van 1-5 sterren
- `review`: Tekst van de review
- `is_approved`: Boolean voor goedkeuring status
- `sort_order`: Volgorde voor sortering
- `created_at`: Aanmaakdatum
- `updated_at`: Laatste wijziging
- `deleted_at`: Soft delete timestamp

## Model Eigenschappen

### Traits
- `CanRedirectModelTrait`: Automatische redirects bij verwijdering
- `HasPageFinderTrait`: PageFinder integratie
- `Sluggable`: Automatische slug generatie
- `SoftDelete`: Soft delete functionaliteit
- `Sortable`: Sorteerbare records
- `Validation`: Model validatie

### Validatie Regels
- `name`: Verplicht, string, max 255 karakters
- `slug`: Verplicht, string, max 255 karakters, uniek
- `date`: Optioneel, geldige datum
- `score`: Optioneel, integer tussen 1-5
- `review`: Optioneel, string
- `is_approved`: Boolean
- `sort_order`: Integer

### Translatable Fields
- `name`: Naam van reviewer
- `slug`: URL slug
- `review`: Review tekst

## Controller Functionaliteiten

### Reviews Controller
- **Traits**: PluginDuplicateTrait, PluginImportExportTrait, PluginSoftDeleteTrait
- **Behaviors**: FormController, ListController, ImportExportController
- **Soft Delete**: Volledig ondersteund met restore functionaliteit
- **Duplicate**: Reviews kunnen worden gedupliceerd
- **Import/Export**: Volledige import/export ondersteuning

### List Features
- **Filtering**: Filter op verwijderde items
- **Sorting**: Sorteerbaar op alle relevante velden
- **Search**: Zoeken in naam en slug
- **Bulk Actions**: Bulk delete en restore
- **Actions Column**: Duplicate, Delete, Restore knoppen

## Settings

De plugin biedt instellingen voor:
- **Testimonials Page**: Hoofdpagina voor testimonials
- **Review Page**: Detailpagina voor individuele reviews

## Menu Structuur

### Content Section
- **Reviews**: Beheer van alle reviews

### Settings Section
- **General**: Algemene plugin instellingen

### Documentation Section
- **Readme**: Deze documentatie (alleen voor superusers)
- **Manual**: Gebruikershandleiding

## PageFinder Integratie

De plugin ondersteunt PageFinder met:
- **Single Type**: `testimonials-review` voor individuele reviews
- **All Type**: `all-testimonials-reviews` voor alle reviews
- **Component**: ReviewDetail voor detail weergave

## Gebruikte Permissions

Alle unieke permissions gebruikt in deze plugin:
- `invato.testimonials.manage_plugin`
- `invato.testimonials.manage_reviews`
- `invato.testimonials.approve_reviews`
- `invato.testimonials.import_reviews`
- `invato.testimonials.export_reviews`
- `superusers.view_readme`

## Technische Details

### Namespace
`Invato\Testimonials`

### Models
- `Review`: Hoofdmodel voor reviews
- `ReviewImportModel`: Import model
- `ReviewExportModel`: Export model
- `TestimonialsSettings`: Settings model

### Components
- `ReviewList`: Lijst component
- `ReviewDetail`: Detail component

### Controllers
- `Reviews`: Hoofdcontroller
- `Readme`: README controller
- `Manual`: Manual controller

## Installatie

1. Plaats de plugin in `plugins/invato/testimonials/`
2. Voer `php artisan october:migrate` uit
3. Configureer de permissions in het backend
4. Stel de pagina's in via Settings

## Onderhoud

Deze plugin volgt de Invato standaarden en is volledig compatibel met:
- OctoberCMS v3.6+
- PHP v8.3+
- MySQL v8+
- Laravel 10+
- PSR-4 en PSR-12 standaarden

---

## Gebruikte Unieke Permissions

- `invato.testimonials.manage_plugin`
- `invato.testimonials.manage_reviews`
- `invato.testimonials.approve_reviews`
- `invato.testimonials.import_reviews`
- `invato.testimonials.export_reviews`
- `superusers.view_readme`
