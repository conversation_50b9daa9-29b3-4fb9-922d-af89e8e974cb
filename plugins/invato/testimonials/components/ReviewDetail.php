<?php

namespace Invato\Testimonials\Components;

use Cms\Classes\ComponentBase;
use Invato\Testimonials\Models\Review;

class ReviewDetail extends ComponentBase
{
    public function componentDetails(): array
    {
        return [
            'name' => 'invato.testimonials::lang.components.review_detail.name',
            'description' => 'invato.testimonials::lang.components.review_detail.description'
        ];
    }

    public function defineProperties(): array
    {
        return [
            'slug' => [
                'title' => 'invato.testimonials::lang.components.review_detail.slug_title',
                'description' => 'invato.testimonials::lang.components.review_detail.slug_description',
                'default' => '{{ :slug }}',
                'type' => 'string',
            ],
        ];
    }

    public function onRun(): void
    {
        $this->review = $this->loadReview();
    }

    protected function loadReview(): ?Review
    {
        $slug = $this->property('slug');

        if (!$slug) {
            return null;
        }

        return Review::where('slug', $slug)
            ->where('is_approved', true)
            ->first();
    }

    public function review(): ?Review
    {
        return $this->review ?? null;
    }
}
